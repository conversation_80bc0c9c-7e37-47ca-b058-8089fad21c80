"use client";
import React, { useEffect, useState } from "react";
import { useInterview } from "@/context/InterviewContext";
import { Button } from "@/components/ui/button";
import { Loader2, MessageCircle } from "lucide-react";

interface AgentChatProps {
  className?: string;
  onMessageSent?: () => void;
  onChatReady?: () => void;
  autoSendMessage?: string;
}

const AgentChat: React.FC<AgentChatProps> = ({
  className = "",
  onMessageSent,
  onChatReady,
  autoSendMessage,
}) => {
  const {
    agent,
    streamConnection,
    isConnectingStream,
    streamError,
    createStreamConnection,
    isSendingMessage,
    messageError,
    sendMessage,
  } = useInterview();

  const [hasInitialized, setHasInitialized] = useState(false);

  // Create stream connection when agent is ready
  useEffect(() => {
    if (agent && !streamConnection && !isConnectingStream && !hasInitialized) {
      console.log("AgentChat: Creating stream connection for agent:", agent.id);
      createStreamConnection();
      setHasInitialized(true);
    }
  }, [agent, streamConnection, isConnectingStream, createStreamConnection, hasInitialized]);

  // Notify when stream is ready
  useEffect(() => {
    if (streamConnection && onChatReady) {
      console.log("AgentChat: Stream connection ready, notifying parent");
      onChatReady();
    }
  }, [streamConnection, onChatReady]);

  // Auto-send message when provided
  useEffect(() => {
    if (autoSendMessage && streamConnection && !isSendingMessage) {
      console.log("Auto-sending message:", autoSendMessage);
      sendMessage(autoSendMessage).then(() => {
        onMessageSent?.();
      });
    }
  }, [autoSendMessage, streamConnection, isSendingMessage, sendMessage, onMessageSent]);

  const handleSendMessage = async (message: string) => {
    if (!message.trim()) return;
    
    try {
      await sendMessage(message);
      onMessageSent?.();
    } catch (error) {
      console.error("Failed to send message:", error);
    }
  };

  const isReady = streamConnection;
  const isLoading = isConnectingStream || isSendingMessage;

  if (messageError || streamError) {
    return (
      <div className={`flex items-center justify-center p-4 ${className}`}>
        <div className="text-center">
          <MessageCircle className="w-8 h-8 text-red-500 mx-auto mb-2" />
          <p className="text-red-600 text-sm">Error:</p>
          <p className="text-red-500 text-xs mt-1">{messageError || streamError}</p>
        </div>
      </div>
    );
  }

  if (!isReady) {
    return (
      <div className={`flex items-center justify-center p-4 ${className}`}>
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-2" />
          <p className="text-gray-600 text-sm">
            {!agent ? "Waiting for agent..." :
             !streamConnection ? "Connecting to stream..." : "Setting up..."}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`${className}`}>
      {/* Stream Status */}
      <div className="flex items-center justify-center mb-4">
        <div className="flex items-center gap-2 text-sm text-green-600">
          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          <span>Stream Ready</span>
        </div>
      </div>

      {/* Loading indicator when sending message */}
      {isLoading && (
        <div className="flex items-center justify-center p-4">
          <div className="text-center">
            <Loader2 className="w-6 h-6 animate-spin text-blue-600 mx-auto mb-2" />
            <p className="text-gray-600 text-sm">
              {isSendingMessage ? "Sending message..." : "Setting up stream..."}
            </p>
          </div>
        </div>
      )}

      {/* Debug Info (can be removed in production) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="mt-4 p-2 bg-gray-100 rounded text-xs">
          <p>Agent ID: {agent?.id}</p>
          <p>Stream ID: {streamConnection?.streamId}</p>
          <p>Session ID: {streamConnection?.sessionId}</p>
        </div>
      )}
    </div>
  );
};

export default AgentChat;
