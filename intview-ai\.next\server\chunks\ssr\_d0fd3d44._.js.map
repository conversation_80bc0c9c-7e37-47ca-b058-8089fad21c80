{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean\r\n  }) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/interview/InterviewInstructions.tsx"], "sourcesContent": ["\"use client\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { ArrowRight } from \"lucide-react\";\r\nimport React, { useState } from \"react\";\r\n\r\ntype InterviewInstructionsProps = {\r\n  candidateName?: string;\r\n  jobTitle?: string;\r\n  languages?: string[];\r\n  instructions?: string[];\r\n  environmentChecklist?: string[];\r\n  disclaimers?: string[];\r\n  onNext?: () => void;\r\n};\r\n\r\nconst defaultInstructions = [\r\n  \"The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.\",\r\n  \"The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.\",\r\n  \"The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.\",\r\n  \"The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.\",\r\n  \"The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.\",\r\n];\r\n\r\nconst defaultEnvironment = [\r\n  \"To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.\",\r\n  \"To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.\",\r\n  \"To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.\",\r\n  \"To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.\",\r\n];\r\n\r\nconst defaultDisclaimers = [\r\n  \"Environment Requirements Ensure you are in a quiet, distraction-free space. Sit in a well-lit area so the avatar can see you clearly. Use a stable internet connection and a working camera & microphone .\",\r\n  \"AI Interview Format Your interviewer will be an AI avatar, speaking and listening in a natural, conversational style. You will respond to 5 preset questions, with roughly under 10 minutes total interview time. You may be gently prompted if your answers run long—please stay within the time suggested .\",\r\n  \"Recording & Usage This session will be fully recorded (audio & video) for review by our hiring team. Your responses and the recording will be processed by our AI scoring system to evaluate communication, problem-solving, and fit. All data is stored securely and used only for the purposes of hiring this role .\",\r\n  \"Independence & Integrity Please answer without external aids (notes, websites, or other people). If background noise or interruptions occur, you may be prompted to pause and restart your answer .\",\r\n];\r\n\r\nconst InterviewInstructions: React.FC<InterviewInstructionsProps> = ({\r\n  candidateName = \"Jonathan\",\r\n  jobTitle = \"Insurance Agent\",\r\n  languages = [\"English\", \"Chinese\"],\r\n  instructions = defaultInstructions,\r\n  environmentChecklist = defaultEnvironment,\r\n  disclaimers = defaultDisclaimers,\r\n  onNext,\r\n}) => {\r\n  const [isChecked, setIsChecked] = useState(false);\r\n\r\n  return (\r\n    <div className=\"flex-1 border border-gray-400 rounded-md h-fit bg-white\">\r\n      <div className=\"p-4 flex flex-col text-[#38383a]\">\r\n        <p className=\"font-semibold mb-8 text-xl\">\r\n          Instructions for Interview!\r\n        </p>\r\n        <div className=\"space-y-6\">\r\n          <div>\r\n            <p className=\" mb-2 text-md\">Hello {candidateName}!</p>\r\n            <p className=\"text-sm mb-4\">\r\n              As part of the process you are required to complete an AI video\r\n              assessment for the role of the {jobTitle}.\r\n            </p>\r\n          </div>\r\n\r\n          <div>\r\n            <p className=\"font-semibold mb-2 text-lg\">Interview Language</p>\r\n            <ul className=\"list-disc list-inside space-y-2 text-sm\">\r\n              {languages.map((language, index) => (\r\n                <li key={index}>{language}</li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n\r\n          <div>\r\n            <p className=\"font-semibold mb-2 text-lg\">Instructions</p>\r\n            <ul className=\"list-disc list-inside space-y-2 text-sm\">\r\n              {instructions.map((instruction, index) => (\r\n                <li key={index}>{instruction}</li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n\r\n          <div>\r\n            <p className=\"font-semibold mb-2 text-lg\">Environment Checklist:</p>\r\n            <ul className=\"list-disc list-inside space-y-2 text-sm\">\r\n              {environmentChecklist.map((item, index) => (\r\n                <li key={index}>{item}</li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n\r\n          <div>\r\n            <p className=\"font-semibold mb-2 text-lg\">Important Disclaimers:</p>\r\n            <ul className=\"list-disc list-inside space-y-2 text-sm\">\r\n              {disclaimers.map((disclaimer, index) => (\r\n                <li key={index}>{disclaimer}</li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n\r\n          <div className=\"flex items-start gap-2 mt-6\">\r\n            <input\r\n              type=\"checkbox\"\r\n              id=\"terms\"\r\n              checked={isChecked}\r\n              onChange={(e) => setIsChecked(e.target.checked)}\r\n              className=\"h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\"\r\n            />\r\n            <label htmlFor=\"terms\" className=\"text-[11px] text-[#38383a]\">\r\n              By checking this box, you agree with AI Interview{\" \"}\r\n              <span className=\"text-primary cursor-pointer font-medium\">\r\n                Terms of use\r\n              </span>\r\n              .\r\n            </label>\r\n          </div>\r\n          <div className=\"flex justify-center\">\r\n            <Button\r\n              disabled={!isChecked}\r\n              variant=\"default\"\r\n              size=\"lg\"\r\n              className=\"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white\"\r\n              onClick={() => onNext && onNext()}\r\n            >\r\n            Proceed\r\n              <ArrowRight className=\"w-6 h-6 duration-300 group-hover:translate-x-1\" />\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default InterviewInstructions;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAHA;;;;;AAeA,MAAM,sBAAsB;IAC1B;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,qBAAqB;IACzB;IACA;IACA;IACA;CACD;AAED,MAAM,qBAAqB;IACzB;IACA;IACA;IACA;CACD;AAED,MAAM,wBAA8D,CAAC,EACnE,gBAAgB,UAAU,EAC1B,WAAW,iBAAiB,EAC5B,YAAY;IAAC;IAAW;CAAU,EAClC,eAAe,mBAAmB,EAClC,uBAAuB,kBAAkB,EACzC,cAAc,kBAAkB,EAChC,MAAM,EACP;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAE,WAAU;8BAA6B;;;;;;8BAG1C,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAE,WAAU;;wCAAgB;wCAAO;wCAAc;;;;;;;8CAClD,8OAAC;oCAAE,WAAU;;wCAAe;wCAEM;wCAAS;;;;;;;;;;;;;sCAI7C,8OAAC;;8CACC,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAC1C,8OAAC;oCAAG,WAAU;8CACX,UAAU,GAAG,CAAC,CAAC,UAAU,sBACxB,8OAAC;sDAAgB;2CAAR;;;;;;;;;;;;;;;;sCAKf,8OAAC;;8CACC,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAC1C,8OAAC;oCAAG,WAAU;8CACX,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,8OAAC;sDAAgB;2CAAR;;;;;;;;;;;;;;;;sCAKf,8OAAC;;8CACC,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAC1C,8OAAC;oCAAG,WAAU;8CACX,qBAAqB,GAAG,CAAC,CAAC,MAAM,sBAC/B,8OAAC;sDAAgB;2CAAR;;;;;;;;;;;;;;;;sCAKf,8OAAC;;8CACC,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAC1C,8OAAC;oCAAG,WAAU;8CACX,YAAY,GAAG,CAAC,CAAC,YAAY,sBAC5B,8OAAC;sDAAgB;2CAAR;;;;;;;;;;;;;;;;sCAKf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,IAAG;oCACH,SAAS;oCACT,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,OAAO;oCAC9C,WAAU;;;;;;8CAEZ,8OAAC;oCAAM,SAAQ;oCAAQ,WAAU;;wCAA6B;wCACV;sDAClD,8OAAC;4CAAK,WAAU;sDAA0C;;;;;;wCAEnD;;;;;;;;;;;;;sCAIX,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,2HAAA,CAAA,SAAM;gCACL,UAAU,CAAC;gCACX,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,UAAU;;oCAC1B;kDAEC,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC;uCAEe", "debugId": null}}, {"offset": {"line": 378, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/JobInfoCard.tsx"], "sourcesContent": ["import { MapPin, BriefcaseBusiness } from \"lucide-react\";\r\n\r\nconst JobInfoCard = () => {\r\n  return (\r\n    <div className=\"bg-white p-4 rounded-2xl shadow-sm mb-6 max-w-xl\">\r\n      <div className=\"flex justify-between items-start\">\r\n        <div>\r\n          <h2 className=\"text-xl font-semibold mb-3\">\r\n            UX/UI Designer for Ai-Interview Web App\r\n          </h2>\r\n          <div className=\"flex gap-2 leading-relaxed mb-3 flex-wrap\">\r\n            <p className=\"text-sm text-gray-600 font-medium\">\r\n              $500 - $1000 <span className=\"font-extrabold px-1\">·</span>\r\n            </p>\r\n            <div className=\"flex gap-1 items-center\">\r\n              <MapPin className=\"w-4 h-5\" />\r\n              <p className=\"text-sm text-gray-600 font-medium\">New York</p>\r\n            </div>\r\n            <div className=\"flex gap-1 items-center\">\r\n              <BriefcaseBusiness className=\"w-4 h-5\" />\r\n              <p className=\"text-sm text-gray-600 font-medium\">\r\n                Onsite / Remote\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <p className=\"text-sm text-gray-500 mt-1\">\r\n            We&apos;re building an AI-powered interview tool. We expect you to\r\n            help users prepare by giving human interview experience generation.\r\n          </p>\r\n        </div>\r\n        <span className=\"text-xs bg-[#CCFFB1] text-green-700 px-3 py-1 rounded-full font-medium\">\r\n          Active\r\n        </span>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default JobInfoCard;\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;;AAEA,MAAM,cAAc;IAClB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;;sCACC,8OAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAG3C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;;wCAAoC;sDAClC,8OAAC;4CAAK,WAAU;sDAAsB;;;;;;;;;;;;8CAErD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;8CAEnD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gOAAA,CAAA,oBAAiB;4CAAC,WAAU;;;;;;sDAC7B,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;sCAKrD,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;8BAK5C,8OAAC;oBAAK,WAAU;8BAAyE;;;;;;;;;;;;;;;;;AAMjG;uCAEe", "debugId": null}}, {"offset": {"line": 518, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/context/InterviewContext.tsx"], "sourcesContent": ["\"use client\";\nimport React, { createContext, useContext, useState, useCallback, ReactNode } from \"react\";\n\nconst DID_API_URL = \"https://api.d-id.com\";\n\ninterface Agent {\n  id: string;\n  preview_name: string;\n  status: string;\n  presenter: {\n    type: string;\n    voice: {\n      type: string;\n      voice_id: string;\n    };\n    thumbnail: string;\n    source_url: string;\n  };\n  llm: {\n    type: string;\n    provider: string;\n    model: string;\n    instructions: string;\n  };\n}\n\ninterface ChatSession {\n  id: string;\n  agent_id: string;\n  created: string;\n  modified: string;\n  messages: Array<{\n    role: string;\n    content: string;\n    created_at: string;\n  }>;\n  owner_id: string;\n}\n\ninterface StreamConnection {\n  streamId: string;\n  sessionId: string;\n  iceServers: Array<{\n    urls: string[];\n    username?: string;\n    credential?: string;\n  }>;\n}\n\ninterface InterviewContextType {\n  // D-ID Agent state\n  agent: Agent | null;\n  isCreatingAgent: boolean;\n  agentError: string | null;\n  createAgent: (instructions: string, agentName: string) => Promise<void>;\n\n  // Chat Session state\n  chatSession: ChatSession | null;\n  isCreatingChat: boolean;\n  chatError: string | null;\n  createChatSession: () => Promise<void>;\n\n  // Stream Connection state\n  streamConnection: StreamConnection | null;\n  isConnectingStream: boolean;\n  streamError: string | null;\n  createStreamConnection: () => Promise<void>;\n\n  // Message sending\n  isSendingMessage: boolean;\n  messageError: string | null;\n  sendMessage: (message: string) => Promise<void>;\n\n  // Interview state\n  currentQuestion: number;\n  setCurrentQuestion: (question: number) => void;\n  isInterviewStarted: boolean;\n  setIsInterviewStarted: (started: boolean) => void;\n\n  // Questions data\n  questions: string[];\n}\n\nconst InterviewContext = createContext<InterviewContextType | undefined>(undefined);\n\ninterface InterviewProviderProps {\n  children: ReactNode;\n}\n\nexport const InterviewProvider: React.FC<InterviewProviderProps> = ({ children }) => {\n  // D-ID Agent state\n  const [agent, setAgent] = useState<Agent | null>(null);\n  const [isCreatingAgent, setIsCreatingAgent] = useState<boolean>(false);\n  const [agentError, setAgentError] = useState<string | null>(null);\n\n  // Chat Session state\n  const [chatSession, setChatSession] = useState<ChatSession | null>(null);\n  const [isCreatingChat, setIsCreatingChat] = useState<boolean>(false);\n  const [chatError, setChatError] = useState<string | null>(null);\n\n  // Stream Connection state\n  const [streamConnection, setStreamConnection] = useState<StreamConnection | null>(null);\n  const [isConnectingStream, setIsConnectingStream] = useState<boolean>(false);\n  const [streamError, setStreamError] = useState<string | null>(null);\n\n  // Message sending state\n  const [isSendingMessage, setIsSendingMessage] = useState<boolean>(false);\n  const [messageError, setMessageError] = useState<string | null>(null);\n\n  // Interview state\n  const [currentQuestion, setCurrentQuestion] = useState<number>(1);\n  const [isInterviewStarted, setIsInterviewStarted] = useState<boolean>(false);\n  \n  // Questions data\n  const questions = [\n    \"Tell us about yourself?\",\n    \"What are your strengths?\",\n    \"Why do you want this job?\",\n    \"Where do you see yourself in 5 years?\",\n  ];\n\n  const getAuthHeaders = () => {\n    const apiKey = process.env.NEXT_PUBLIC_DID_API_KEY || process.env.DID_API_KEY || \"\";\n    console.log(\"Using D-ID API Key:\", apiKey ? `${apiKey.substring(0, 10)}...` : \"NOT_FOUND\");\n\n    return {\n      \"Authorization\": `Basic ${apiKey}`,\n      \"Content-Type\": \"application/json\",\n    };\n  };\n\n  const createAgent = useCallback(async (instructions: string, agentName: string) => {\n    // If agent already exists with same instructions, don't recreate\n    if (agent && agent.llm.instructions === instructions && agent.preview_name === agentName) {\n      return;\n    }\n\n    setIsCreatingAgent(true);\n    setAgentError(null);\n\n    const payload = {\n      presenter: {\n        type: \"talk\",\n        voice: {\n          type: \"microsoft\",\n          voice_id: \"en-US-JennyMultilingualV2Neural\"\n        },\n        thumbnail: \"https://create-images-results.d-id.com/DefaultPresenters/Zivva_f/thumbnail.jpeg\",\n        source_url: \"https://create-images-results.d-id.com/DefaultPresenters/Zivva_f/thumbnail.jpeg\"\n      },\n      llm: {\n        type: \"openai\",\n        provider: \"openai\",\n        model: \"gpt-4o-mini\",\n        instructions: instructions\n      },\n      preview_name: agentName\n    };\n\n    try {\n      console.log(\"Creating D-ID Agent with payload:\", payload);\n\n      const response = await fetch(`${DID_API_URL}/agents`, {\n        method: \"POST\",\n        headers: getAuthHeaders(),\n        body: JSON.stringify(payload),\n      });\n\n      console.log(\"D-ID Agent API Response Status:\", response.status);\n\n      if (!response.ok) {\n        const errorText = await response.text();\n        console.error(\"D-ID Agent API Error Response:\", errorText);\n        throw new Error(`Failed to create agent: ${response.status} ${response.statusText} - ${errorText}`);\n      }\n\n      const agentData: Agent = await response.json();\n      console.log(\"D-ID Agent Created Successfully:\", agentData);\n      setAgent(agentData);\n    } catch (err: unknown) {\n      console.error(\"D-ID Agent Creation Error:\", err);\n      const errorMessage = err instanceof Error ? err.message : \"Failed to create agent\";\n      setAgentError(`Agent Creation Failed: ${errorMessage}`);\n    } finally {\n      setIsCreatingAgent(false);\n    }\n  }, [agent]);\n\n  const createChatSession = useCallback(async () => {\n    if (!agent) {\n      setChatError(\"No agent available to create chat session\");\n      return;\n    }\n\n    setIsCreatingChat(true);\n    setChatError(null);\n\n    try {\n      console.log(\"Creating chat session for agent:\", agent.id);\n\n      const response = await fetch(`${DID_API_URL}/agents/${agent.id}/chat`, {\n        method: \"POST\",\n        headers: getAuthHeaders(),\n        body: JSON.stringify({}), // Empty body as per documentation\n      });\n\n      console.log(\"Chat Session API Response Status:\", response.status);\n\n      if (!response.ok) {\n        const errorText = await response.text();\n        console.error(\"Chat Session API Error Response:\", errorText);\n        throw new Error(`Failed to create chat session: ${response.status} ${response.statusText} - ${errorText}`);\n      }\n\n      const chatData: ChatSession = await response.json();\n      console.log(\"Chat Session Created Successfully:\", chatData);\n      setChatSession(chatData);\n    } catch (err: unknown) {\n      console.error(\"Chat Session Creation Error:\", err);\n      const errorMessage = err instanceof Error ? err.message : \"Failed to create chat session\";\n      setChatError(`Chat Session Creation Failed: ${errorMessage}`);\n    } finally {\n      setIsCreatingChat(false);\n    }\n  }, [agent]);\n\n  const createStreamConnection = useCallback(async () => {\n    if (!agent) {\n      setStreamError(\"No agent available to create stream connection\");\n      return;\n    }\n\n    setIsConnectingStream(true);\n    setStreamError(null);\n\n    try {\n      console.log(\"Creating stream connection for agent:\", agent.id);\n\n      // Step 1: Create a new stream\n      const streamResponse = await fetch(`${DID_API_URL}/agents/${agent.id}/streams`, {\n        method: \"POST\",\n        headers: getAuthHeaders(),\n        body: JSON.stringify({}), // Empty body as per documentation\n      });\n\n      if (!streamResponse.ok) {\n        const errorText = await streamResponse.text();\n        throw new Error(`Failed to create stream: ${streamResponse.status} ${streamResponse.statusText} - ${errorText}`);\n      }\n\n      const streamData = await streamResponse.json();\n      console.log(\"Stream Created Successfully:\", streamData);\n\n      const connectionData: StreamConnection = {\n        streamId: streamData.id,\n        sessionId: streamData.session_id,\n        iceServers: streamData.ice_servers || []\n      };\n\n      setStreamConnection(connectionData);\n    } catch (err: unknown) {\n      console.error(\"Stream Connection Creation Error:\", err);\n      const errorMessage = err instanceof Error ? err.message : \"Failed to create stream connection\";\n      setStreamError(`Stream Connection Failed: ${errorMessage}`);\n    } finally {\n      setIsConnectingStream(false);\n    }\n  }, [agent]);\n\n  const sendMessage = useCallback(async (message: string) => {\n    if (!agent || !chatSession || !streamConnection) {\n      setMessageError(\"Agent, chat session, or stream connection not available\");\n      return;\n    }\n\n    setIsSendingMessage(true);\n    setMessageError(null);\n\n    try {\n      console.log(\"Sending message to agent:\", message);\n\n      const messagePayload = {\n        streamId: streamConnection.streamId,\n        sessionId: streamConnection.sessionId,\n        messages: [\n          {\n            role: \"user\",\n            content: message,\n            created_at: new Date().toLocaleString()\n          }\n        ]\n      };\n\n      const response = await fetch(`${DID_API_URL}/agents/${agent.id}/chat/${chatSession.id}`, {\n        method: \"POST\",\n        headers: getAuthHeaders(),\n        body: JSON.stringify(messagePayload),\n      });\n\n      console.log(\"Send Message API Response Status:\", response.status);\n\n      if (!response.ok) {\n        const errorText = await response.text();\n        console.error(\"Send Message API Error Response:\", errorText);\n        throw new Error(`Failed to send message: ${response.status} ${response.statusText} - ${errorText}`);\n      }\n\n      console.log(\"Message sent successfully\");\n      // The video response will be streamed via WebRTC connection\n    } catch (err: unknown) {\n      console.error(\"Send Message Error:\", err);\n      const errorMessage = err instanceof Error ? err.message : \"Failed to send message\";\n      setMessageError(`Send Message Failed: ${errorMessage}`);\n    } finally {\n      setIsSendingMessage(false);\n    }\n  }, [agent, chatSession, streamConnection]);\n\n  const value: InterviewContextType = {\n    // D-ID Agent state\n    agent,\n    isCreatingAgent,\n    agentError,\n    createAgent,\n\n    // Chat Session state\n    chatSession,\n    isCreatingChat,\n    chatError,\n    createChatSession,\n\n    // Stream Connection state\n    streamConnection,\n    isConnectingStream,\n    streamError,\n    createStreamConnection,\n\n    // Message sending\n    isSendingMessage,\n    messageError,\n    sendMessage,\n\n    // Interview state\n    currentQuestion,\n    setCurrentQuestion,\n    isInterviewStarted,\n    setIsInterviewStarted,\n\n    // Questions data\n    questions,\n  };\n\n  return (\n    <InterviewContext.Provider value={value}>\n      {children}\n    </InterviewContext.Provider>\n  );\n};\n\nexport const useInterview = (): InterviewContextType => {\n  const context = useContext(InterviewContext);\n  if (context === undefined) {\n    throw new Error('useInterview must be used within an InterviewProvider');\n  }\n  return context;\n};\n"], "names": [], "mappings": ";;;;;AACA;AADA;;;AAGA,MAAM,cAAc;AAgFpB,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAoC;AAMlE,MAAM,oBAAsD,CAAC,EAAE,QAAQ,EAAE;IAC9E,mBAAmB;IACnB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAChE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE5D,qBAAqB;IACrB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IACnE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAC9D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE1D,0BAA0B;IAC1B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B;IAClF,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IACtE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE9D,wBAAwB;IACxB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAClE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEhE,kBAAkB;IAClB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC/D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAEtE,iBAAiB;IACjB,MAAM,YAAY;QAChB;QACA;QACA;QACA;KACD;IAED,MAAM,iBAAiB;QACrB,MAAM,SAAS,4FAAuC,QAAQ,GAAG,CAAC,WAAW,IAAI;QACjF,QAAQ,GAAG,CAAC,uBAAuB,uCAAS,GAAG,OAAO,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC;QAE3E,OAAO;YACL,iBAAiB,CAAC,MAAM,EAAE,QAAQ;YAClC,gBAAgB;QAClB;IACF;IAEA,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,cAAsB;QAC3D,iEAAiE;QACjE,IAAI,SAAS,MAAM,GAAG,CAAC,YAAY,KAAK,gBAAgB,MAAM,YAAY,KAAK,WAAW;YACxF;QACF;QAEA,mBAAmB;QACnB,cAAc;QAEd,MAAM,UAAU;YACd,WAAW;gBACT,MAAM;gBACN,OAAO;oBACL,MAAM;oBACN,UAAU;gBACZ;gBACA,WAAW;gBACX,YAAY;YACd;YACA,KAAK;gBACH,MAAM;gBACN,UAAU;gBACV,OAAO;gBACP,cAAc;YAChB;YACA,cAAc;QAChB;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC,qCAAqC;YAEjD,MAAM,WAAW,MAAM,MAAM,GAAG,YAAY,OAAO,CAAC,EAAE;gBACpD,QAAQ;gBACR,SAAS;gBACT,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,QAAQ,GAAG,CAAC,mCAAmC,SAAS,MAAM;YAE9D,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,QAAQ,KAAK,CAAC,kCAAkC;gBAChD,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,CAAC,GAAG,EAAE,WAAW;YACpG;YAEA,MAAM,YAAmB,MAAM,SAAS,IAAI;YAC5C,QAAQ,GAAG,CAAC,oCAAoC;YAChD,SAAS;QACX,EAAE,OAAO,KAAc;YACrB,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,cAAc,CAAC,uBAAuB,EAAE,cAAc;QACxD,SAAU;YACR,mBAAmB;QACrB;IACF,GAAG;QAAC;KAAM;IAEV,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACpC,IAAI,CAAC,OAAO;YACV,aAAa;YACb;QACF;QAEA,kBAAkB;QAClB,aAAa;QAEb,IAAI;YACF,QAAQ,GAAG,CAAC,oCAAoC,MAAM,EAAE;YAExD,MAAM,WAAW,MAAM,MAAM,GAAG,YAAY,QAAQ,EAAE,MAAM,EAAE,CAAC,KAAK,CAAC,EAAE;gBACrE,QAAQ;gBACR,SAAS;gBACT,MAAM,KAAK,SAAS,CAAC,CAAC;YACxB;YAEA,QAAQ,GAAG,CAAC,qCAAqC,SAAS,MAAM;YAEhE,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,MAAM,IAAI,MAAM,CAAC,+BAA+B,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,CAAC,GAAG,EAAE,WAAW;YAC3G;YAEA,MAAM,WAAwB,MAAM,SAAS,IAAI;YACjD,QAAQ,GAAG,CAAC,sCAAsC;YAClD,eAAe;QACjB,EAAE,OAAO,KAAc;YACrB,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,aAAa,CAAC,8BAA8B,EAAE,cAAc;QAC9D,SAAU;YACR,kBAAkB;QACpB;IACF,GAAG;QAAC;KAAM;IAEV,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACzC,IAAI,CAAC,OAAO;YACV,eAAe;YACf;QACF;QAEA,sBAAsB;QACtB,eAAe;QAEf,IAAI;YACF,QAAQ,GAAG,CAAC,yCAAyC,MAAM,EAAE;YAE7D,8BAA8B;YAC9B,MAAM,iBAAiB,MAAM,MAAM,GAAG,YAAY,QAAQ,EAAE,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE;gBAC9E,QAAQ;gBACR,SAAS;gBACT,MAAM,KAAK,SAAS,CAAC,CAAC;YACxB;YAEA,IAAI,CAAC,eAAe,EAAE,EAAE;gBACtB,MAAM,YAAY,MAAM,eAAe,IAAI;gBAC3C,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,eAAe,MAAM,CAAC,CAAC,EAAE,eAAe,UAAU,CAAC,GAAG,EAAE,WAAW;YACjH;YAEA,MAAM,aAAa,MAAM,eAAe,IAAI;YAC5C,QAAQ,GAAG,CAAC,gCAAgC;YAE5C,MAAM,iBAAmC;gBACvC,UAAU,WAAW,EAAE;gBACvB,WAAW,WAAW,UAAU;gBAChC,YAAY,WAAW,WAAW,IAAI,EAAE;YAC1C;YAEA,oBAAoB;QACtB,EAAE,OAAO,KAAc;YACrB,QAAQ,KAAK,CAAC,qCAAqC;YACnD,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,eAAe,CAAC,0BAA0B,EAAE,cAAc;QAC5D,SAAU;YACR,sBAAsB;QACxB;IACF,GAAG;QAAC;KAAM;IAEV,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACrC,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,kBAAkB;YAC/C,gBAAgB;YAChB;QACF;QAEA,oBAAoB;QACpB,gBAAgB;QAEhB,IAAI;YACF,QAAQ,GAAG,CAAC,6BAA6B;YAEzC,MAAM,iBAAiB;gBACrB,UAAU,iBAAiB,QAAQ;gBACnC,WAAW,iBAAiB,SAAS;gBACrC,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS;wBACT,YAAY,IAAI,OAAO,cAAc;oBACvC;iBACD;YACH;YAEA,MAAM,WAAW,MAAM,MAAM,GAAG,YAAY,QAAQ,EAAE,MAAM,EAAE,CAAC,MAAM,EAAE,YAAY,EAAE,EAAE,EAAE;gBACvF,QAAQ;gBACR,SAAS;gBACT,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,QAAQ,GAAG,CAAC,qCAAqC,SAAS,MAAM;YAEhE,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,CAAC,GAAG,EAAE,WAAW;YACpG;YAEA,QAAQ,GAAG,CAAC;QACZ,4DAA4D;QAC9D,EAAE,OAAO,KAAc;YACrB,QAAQ,KAAK,CAAC,uBAAuB;YACrC,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,gBAAgB,CAAC,qBAAqB,EAAE,cAAc;QACxD,SAAU;YACR,oBAAoB;QACtB;IACF,GAAG;QAAC;QAAO;QAAa;KAAiB;IAEzC,MAAM,QAA8B;QAClC,mBAAmB;QACnB;QACA;QACA;QACA;QAEA,qBAAqB;QACrB;QACA;QACA;QACA;QAEA,0BAA0B;QAC1B;QACA;QACA;QACA;QAEA,kBAAkB;QAClB;QACA;QACA;QAEA,kBAAkB;QAClB;QACA;QACA;QACA;QAEA,iBAAiB;QACjB;IACF;IAEA,qBACE,8OAAC,iBAAiB,QAAQ;QAAC,OAAO;kBAC/B;;;;;;AAGP;AAEO,MAAM,eAAe;IAC1B,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 779, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/QuestionsList.tsx"], "sourcesContent": ["\"use client\";\r\nimport { useInterview } from \"@/context/InterviewContext\";\r\n\r\ntype QuestionsListProps = {\r\n  className?: string;\r\n};\r\n\r\nconst QuestionsList = ({\r\n  className,\r\n}: QuestionsListProps) => {\r\n  const { questions, currentQuestion } = useInterview();\r\n\r\n  return (\r\n    <div\r\n      className={`rounded-2xl bg-white p-4 w-full max-w-[300px] sm:w-[300px] h-[488px] shadow-sm overflow-y-auto scrollbar-hidden ${\r\n        className || \"\"\r\n      }`}\r\n    >\r\n      {\" \"}\r\n      <h3 className=\"font-semibold text-lg mb-6\">Questions</h3>\r\n      <ul className=\"relative space-y-8  \">\r\n        {Array.from({ length: 4 }, (_, i) => (\r\n          <li\r\n            key={i}\r\n            className=\"relative flex items-start space-x-3 mt-4 mb-0 sm:mb-5\"\r\n          >\r\n            {i !== 3 && (\r\n              <span className=\"absolute left-[17px] pl-[3px] top-6 mt-11 h-10 w-[3px] rounded-full bg-gradient-to-b from-white to-[#6938EF]\" />\r\n            )}\r\n            <div\r\n              className={`rounded-full w-7 h-7 mt-7 flex items-center p-5 justify-center text-sm font-medium z-10 ${\r\n                i + 1 === currentQuestion\r\n                  ? \"bg-[#6938EF] text-white\"\r\n                  : i + 1 < currentQuestion\r\n                    ? \"bg-green-500 text-white\"\r\n                    : \"bg-[#C7ACF5] text-white\"\r\n              }`}\r\n            >\r\n              {i + 1 < currentQuestion ? \"✓\" : i + 1}\r\n            </div>\r\n            <span\r\n              className={`text-md font-medium mt-7 ${\r\n                i + 1 === currentQuestion\r\n                  ? \"text-[#6938EF] font-semibold\"\r\n                  : \"text-[#616161]\"\r\n              }`}\r\n            >\r\n              {questions[i]}\r\n            </span>\r\n          </li>\r\n        ))}\r\n      </ul>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default QuestionsList;\r\n"], "names": [], "mappings": ";;;;AACA;AADA;;;AAOA,MAAM,gBAAgB,CAAC,EACrB,SAAS,EACU;IACnB,MAAM,EAAE,SAAS,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAElD,qBACE,8OAAC;QACC,WAAW,CAAC,gHAAgH,EAC1H,aAAa,IACb;;YAED;0BACD,8OAAC;gBAAG,WAAU;0BAA6B;;;;;;0BAC3C,8OAAC;gBAAG,WAAU;0BACX,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAE,GAAG,CAAC,GAAG,kBAC7B,8OAAC;wBAEC,WAAU;;4BAET,MAAM,mBACL,8OAAC;gCAAK,WAAU;;;;;;0CAElB,8OAAC;gCACC,WAAW,CAAC,wFAAwF,EAClG,IAAI,MAAM,kBACN,4BACA,IAAI,IAAI,kBACN,4BACA,2BACN;0CAED,IAAI,IAAI,kBAAkB,MAAM,IAAI;;;;;;0CAEvC,8OAAC;gCACC,WAAW,CAAC,yBAAyB,EACnC,IAAI,MAAM,kBACN,iCACA,kBACJ;0CAED,SAAS,CAAC,EAAE;;;;;;;uBAxBV;;;;;;;;;;;;;;;;AA+BjB;uCAEe", "debugId": null}}, {"offset": {"line": 856, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/WebRTCStream.tsx"], "sourcesContent": ["\"use client\";\nimport React, { useRef, useEffect, useState, useCallback } from \"react\";\nimport { useInterview } from \"@/context/InterviewContext\";\n\ninterface WebRTCStreamProps {\n  className?: string;\n  onConnectionStateChange?: (state: RTCPeerConnectionState) => void;\n  onVideoReady?: () => void;\n  onVideoEnd?: () => void;\n}\n\nconst WebRTCStream: React.FC<WebRTCStreamProps> = ({\n  className = \"\",\n  onConnectionStateChange,\n  onVideoReady,\n  onVideoEnd,\n}) => {\n  const videoRef = useRef<HTMLVideoElement>(null);\n  const peerConnectionRef = useRef<RTCPeerConnection | null>(null);\n  const [connectionState, setConnectionState] = useState<RTCPeerConnectionState>(\"new\");\n  const [isVideoPlaying, setIsVideoPlaying] = useState(false);\n\n  const { streamConnection, streamError } = useInterview();\n\n  const setupPeerConnection = useCallback(async () => {\n    if (!streamConnection) {\n      console.log(\"No stream connection available\");\n      return;\n    }\n\n    try {\n      console.log(\"Setting up WebRTC peer connection...\");\n      \n      // Create peer connection with ICE servers from D-ID\n      const configuration: RTCConfiguration = {\n        iceServers: streamConnection.iceServers.length > 0 \n          ? streamConnection.iceServers \n          : [{ urls: \"stun:stun.l.google.com:19302\" }] // Fallback STUN server\n      };\n\n      const peerConnection = new RTCPeerConnection(configuration);\n      peerConnectionRef.current = peerConnection;\n\n      // Handle connection state changes\n      peerConnection.onconnectionstatechange = () => {\n        const state = peerConnection.connectionState;\n        console.log(\"WebRTC connection state:\", state);\n        setConnectionState(state);\n        onConnectionStateChange?.(state);\n      };\n\n      // Handle ICE connection state changes\n      peerConnection.oniceconnectionstatechange = () => {\n        console.log(\"ICE connection state:\", peerConnection.iceConnectionState);\n      };\n\n      // Handle incoming media stream\n      peerConnection.ontrack = (event) => {\n        console.log(\"Received remote stream:\", event.streams[0]);\n        if (videoRef.current && event.streams[0]) {\n          videoRef.current.srcObject = event.streams[0];\n          videoRef.current.play().then(() => {\n            console.log(\"Video started playing\");\n            setIsVideoPlaying(true);\n            onVideoReady?.();\n          }).catch((error) => {\n            console.error(\"Error playing video:\", error);\n          });\n        }\n      };\n\n      // Handle ICE candidates\n      peerConnection.onicecandidate = (event) => {\n        if (event.candidate) {\n          console.log(\"New ICE candidate:\", event.candidate);\n          // In a real implementation, you would send this candidate to the D-ID server\n          // For now, we'll log it\n        }\n      };\n\n      // Create offer\n      const offer = await peerConnection.createOffer({\n        offerToReceiveVideo: true,\n        offerToReceiveAudio: true,\n      });\n\n      await peerConnection.setLocalDescription(offer);\n      console.log(\"Created and set local description (offer)\");\n\n      // In a real implementation, you would send this offer to D-ID's WebRTC endpoint\n      // and receive an answer back. For now, we'll simulate this process.\n      console.log(\"Offer SDP:\", offer.sdp);\n\n    } catch (error) {\n      console.error(\"Error setting up peer connection:\", error);\n    }\n  }, [streamConnection, onConnectionStateChange, onVideoReady]);\n\n  const cleanup = useCallback(() => {\n    if (peerConnectionRef.current) {\n      peerConnectionRef.current.close();\n      peerConnectionRef.current = null;\n    }\n    if (videoRef.current) {\n      videoRef.current.srcObject = null;\n    }\n    setIsVideoPlaying(false);\n    setConnectionState(\"new\");\n  }, []);\n\n  useEffect(() => {\n    if (streamConnection) {\n      setupPeerConnection();\n    }\n\n    return cleanup;\n  }, [streamConnection, setupPeerConnection, cleanup]);\n\n  const handleVideoEnded = () => {\n    console.log(\"Video ended\");\n    setIsVideoPlaying(false);\n    onVideoEnd?.();\n  };\n\n  const handleVideoError = (error: React.SyntheticEvent<HTMLVideoElement, Event>) => {\n    console.error(\"Video error:\", error);\n    setIsVideoPlaying(false);\n  };\n\n  if (streamError) {\n    return (\n      <div className={`flex items-center justify-center bg-gray-100 rounded-lg ${className}`}>\n        <div className=\"text-center p-4\">\n          <p className=\"text-red-600 text-sm\">Stream Error:</p>\n          <p className=\"text-red-500 text-xs mt-1\">{streamError}</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!streamConnection) {\n    return (\n      <div className={`flex items-center justify-center bg-gray-100 rounded-lg ${className}`}>\n        <div className=\"text-center p-4\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2\"></div>\n          <p className=\"text-gray-600 text-sm\">Initializing stream...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className={`relative bg-black rounded-lg overflow-hidden ${className}`}>\n      <video\n        ref={videoRef}\n        className=\"w-full h-full object-cover\"\n        autoPlay\n        playsInline\n        muted={false}\n        onEnded={handleVideoEnded}\n        onError={handleVideoError}\n      />\n      \n      {/* Connection status indicator */}\n      <div className=\"absolute top-2 right-2\">\n        <div className={`w-3 h-3 rounded-full ${\n          connectionState === \"connected\" ? \"bg-green-500\" :\n          connectionState === \"connecting\" ? \"bg-yellow-500\" :\n          connectionState === \"failed\" || connectionState === \"disconnected\" ? \"bg-red-500\" :\n          \"bg-gray-500\"\n        }`} title={`Connection: ${connectionState}`} />\n      </div>\n\n      {/* Loading overlay */}\n      {!isVideoPlaying && connectionState !== \"failed\" && (\n        <div className=\"absolute inset-0 flex items-center justify-center bg-black bg-opacity-50\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4\"></div>\n            <p className=\"text-white text-sm\">\n              {connectionState === \"connecting\" ? \"Connecting...\" : \"Loading video...\"}\n            </p>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default WebRTCStream;\n"], "names": [], "mappings": ";;;;AACA;AACA;AAFA;;;;AAWA,MAAM,eAA4C,CAAC,EACjD,YAAY,EAAE,EACd,uBAAuB,EACvB,YAAY,EACZ,UAAU,EACX;IACC,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA4B;IAC3D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;IAC/E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,EAAE,gBAAgB,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAErD,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACtC,IAAI,CAAC,kBAAkB;YACrB,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,oDAAoD;YACpD,MAAM,gBAAkC;gBACtC,YAAY,iBAAiB,UAAU,CAAC,MAAM,GAAG,IAC7C,iBAAiB,UAAU,GAC3B;oBAAC;wBAAE,MAAM;oBAA+B;iBAAE,CAAC,uBAAuB;YACxE;YAEA,MAAM,iBAAiB,IAAI,kBAAkB;YAC7C,kBAAkB,OAAO,GAAG;YAE5B,kCAAkC;YAClC,eAAe,uBAAuB,GAAG;gBACvC,MAAM,QAAQ,eAAe,eAAe;gBAC5C,QAAQ,GAAG,CAAC,4BAA4B;gBACxC,mBAAmB;gBACnB,0BAA0B;YAC5B;YAEA,sCAAsC;YACtC,eAAe,0BAA0B,GAAG;gBAC1C,QAAQ,GAAG,CAAC,yBAAyB,eAAe,kBAAkB;YACxE;YAEA,+BAA+B;YAC/B,eAAe,OAAO,GAAG,CAAC;gBACxB,QAAQ,GAAG,CAAC,2BAA2B,MAAM,OAAO,CAAC,EAAE;gBACvD,IAAI,SAAS,OAAO,IAAI,MAAM,OAAO,CAAC,EAAE,EAAE;oBACxC,SAAS,OAAO,CAAC,SAAS,GAAG,MAAM,OAAO,CAAC,EAAE;oBAC7C,SAAS,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;wBAC3B,QAAQ,GAAG,CAAC;wBACZ,kBAAkB;wBAClB;oBACF,GAAG,KAAK,CAAC,CAAC;wBACR,QAAQ,KAAK,CAAC,wBAAwB;oBACxC;gBACF;YACF;YAEA,wBAAwB;YACxB,eAAe,cAAc,GAAG,CAAC;gBAC/B,IAAI,MAAM,SAAS,EAAE;oBACnB,QAAQ,GAAG,CAAC,sBAAsB,MAAM,SAAS;gBACjD,6EAA6E;gBAC7E,wBAAwB;gBAC1B;YACF;YAEA,eAAe;YACf,MAAM,QAAQ,MAAM,eAAe,WAAW,CAAC;gBAC7C,qBAAqB;gBACrB,qBAAqB;YACvB;YAEA,MAAM,eAAe,mBAAmB,CAAC;YACzC,QAAQ,GAAG,CAAC;YAEZ,gFAAgF;YAChF,oEAAoE;YACpE,QAAQ,GAAG,CAAC,cAAc,MAAM,GAAG;QAErC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;QACrD;IACF,GAAG;QAAC;QAAkB;QAAyB;KAAa;IAE5D,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC1B,IAAI,kBAAkB,OAAO,EAAE;YAC7B,kBAAkB,OAAO,CAAC,KAAK;YAC/B,kBAAkB,OAAO,GAAG;QAC9B;QACA,IAAI,SAAS,OAAO,EAAE;YACpB,SAAS,OAAO,CAAC,SAAS,GAAG;QAC/B;QACA,kBAAkB;QAClB,mBAAmB;IACrB,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,kBAAkB;YACpB;QACF;QAEA,OAAO;IACT,GAAG;QAAC;QAAkB;QAAqB;KAAQ;IAEnD,MAAM,mBAAmB;QACvB,QAAQ,GAAG,CAAC;QACZ,kBAAkB;QAClB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,QAAQ,KAAK,CAAC,gBAAgB;QAC9B,kBAAkB;IACpB;IAEA,IAAI,aAAa;QACf,qBACE,8OAAC;YAAI,WAAW,CAAC,wDAAwD,EAAE,WAAW;sBACpF,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAAuB;;;;;;kCACpC,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;;;;;;IAIlD;IAEA,IAAI,CAAC,kBAAkB;QACrB,qBACE,8OAAC;YAAI,WAAW,CAAC,wDAAwD,EAAE,WAAW;sBACpF,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAI7C;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,6CAA6C,EAAE,WAAW;;0BACzE,8OAAC;gBACC,KAAK;gBACL,WAAU;gBACV,QAAQ;gBACR,WAAW;gBACX,OAAO;gBACP,SAAS;gBACT,SAAS;;;;;;0BAIX,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAW,CAAC,qBAAqB,EACpC,oBAAoB,cAAc,iBAClC,oBAAoB,eAAe,kBACnC,oBAAoB,YAAY,oBAAoB,iBAAiB,eACrE,eACA;oBAAE,OAAO,CAAC,YAAY,EAAE,iBAAiB;;;;;;;;;;;YAI5C,CAAC,kBAAkB,oBAAoB,0BACtC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCACV,oBAAoB,eAAe,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;AAOpE;uCAEe", "debugId": null}}, {"offset": {"line": 1113, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/CandidateWithAgent.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useEffect } from \"react\";\r\nimport Image from \"next/image\";\r\nimport { <PERSON><PERSON>, Loader2 } from \"lucide-react\";\r\n\r\nimport { useInterview } from \"@/context/InterviewContext\";\r\nimport WebRTCStream from \"./WebRTCStream\";\r\n\r\ntype CandidateWithAgentProps = {\r\n  className?: string;\r\n  candidateName?: string;\r\n  jobTitle?: string;\r\n  useAgent?: boolean;\r\n  message?: string;\r\n  onVideoReady?: () => void;\r\n  onVideoEnd?: () => void;\r\n  useStreaming?: boolean;\r\n  avatarMode?: \"standard\" | \"streaming\" | \"live\";\r\n};\r\n\r\nconst CandidateWithAgent: React.FC<CandidateWithAgentProps> = ({\r\n  className = \"\",\r\n  candidateName = \"Jonathan\",\r\n  jobTitle = \"Insurance Agent\",\r\n  useAgent = true,\r\n  useStreaming = false,\r\n  avatarMode = \"standard\",\r\n  onVideoReady,\r\n  onVideoEnd,\r\n}) => {\r\n  const {\r\n    agent,\r\n    isCreatingAgent,\r\n    agentError,\r\n    createAgent,\r\n    streamConnection,\r\n    isConnectingStream,\r\n    streamError,\r\n    createStreamConnection\r\n  } = useInterview();\r\n\r\n  const instructions = `You are an AI interview assistant conducting an interview for the ${jobTitle} position with ${candidateName}. Be professional, engaging, and ask relevant questions about their experience and qualifications.`;\r\n  const agentName = `${jobTitle} Interviewer`;\r\n\r\n  useEffect(() => {\r\n    if (useAgent && !agent && !isCreatingAgent) {\r\n      createAgent(instructions, agentName);\r\n    }\r\n  }, [useAgent, agent, isCreatingAgent, createAgent, instructions, agentName]);\r\n\r\n  // Create stream connection when agent is ready and streaming is enabled\r\n  useEffect(() => {\r\n    if (agent && (avatarMode === \"streaming\" || avatarMode === \"live\") && !streamConnection && !isConnectingStream) {\r\n      createStreamConnection();\r\n    }\r\n  }, [agent, avatarMode, streamConnection, isConnectingStream, createStreamConnection]);\r\n\r\n  return (\r\n    <div className={`relative ${className}`}>\r\n      <div className=\"w-full h-full bg-gradient-to-br from-blue-50 to-indigo-100 rounded-2xl flex flex-col items-center justify-center overflow-hidden\">\r\n        {agent ? (\r\n          <div className=\"text-center w-full h-full flex flex-col\">\r\n            {/* Avatar Display - WebRTC Stream or Static Image */}\r\n            <div className=\"flex-1 flex items-center justify-center p-4\">\r\n              {(avatarMode === \"streaming\" || avatarMode === \"live\") ? (\r\n                <WebRTCStream\r\n                  className=\"w-full h-full max-w-xs max-h-80 rounded-2xl shadow-lg\"\r\n                  onVideoReady={onVideoReady}\r\n                  onVideoEnd={onVideoEnd}\r\n                />\r\n              ) : (\r\n                <>\r\n                  {agent.presenter?.thumbnail ? (\r\n                    <Image\r\n                      src={agent.presenter.thumbnail}\r\n                      alt={agent.preview_name}\r\n                      width={320}\r\n                      height={550}\r\n                      className=\"w-full h-full object-cover rounded-2xl shadow-lg max-w-xs max-h-80\"\r\n                      onError={(e) => {\r\n                        console.error(\"Failed to load avatar image:\", agent.presenter.thumbnail);\r\n                        e.currentTarget.style.display = 'none';\r\n                        e.currentTarget.nextElementSibling?.classList.remove('hidden');\r\n                      }}\r\n                    />\r\n                  ) : (\r\n                    <div className=\"w-32 h-32 bg-gray-300 rounded-full flex items-center justify-center\">\r\n                      <Bot className=\"w-16 h-16 text-gray-600\" />\r\n                    </div>\r\n                  )}\r\n\r\n                  {/* Fallback icon (hidden by default, shown if image fails) */}\r\n                  <div className=\"hidden w-32 h-32 bg-gray-300 rounded-full items-center justify-center\">\r\n                    <Bot className=\"w-16 h-16 text-gray-600\" />\r\n                  </div>\r\n                </>\r\n              )}\r\n            </div>\r\n\r\n            {/* Agent Info */}\r\n            {/* <div className=\"p-4 bg-white/80 backdrop-blur-sm\">\r\n              <h3 className=\"font-semibold text-lg text-gray-800\">\r\n                {agent.preview_name}\r\n              </h3>\r\n              <p className=\"text-sm text-gray-600 mt-1\">\r\n                Status: {agent.status}\r\n              </p>\r\n              {message && (\r\n                <p className=\"text-sm text-blue-600 mt-2 italic\">\r\n                  \"{message}\"\r\n                </p>\r\n              )}\r\n            </div> */}\r\n          </div>\r\n        ) : isCreatingAgent ? (\r\n          <div className=\"text-center\">\r\n            <Loader2 className=\"w-12 h-12 animate-spin text-blue-500 mx-auto mb-4\" />\r\n            <p className=\"text-sm text-gray-600\">Creating AI Agent...</p>\r\n            <p className=\"text-xs text-gray-500 mt-2\">This may take a moment</p>\r\n          </div>\r\n        ) : agentError ? (\r\n          <div className=\"text-center p-4\">\r\n            <div className=\"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\">\r\n              <Bot className=\"w-8 h-8 text-red-500\" />\r\n            </div>\r\n            <p className=\"text-sm text-red-600 mb-2\">Failed to create agent</p>\r\n            <p className=\"text-xs text-gray-500\">{agentError}</p>\r\n            <button\r\n              onClick={() => createAgent(instructions, agentName)}\r\n              className=\"mt-3 px-4 py-2 bg-blue-500 text-white text-xs rounded hover:bg-blue-600 transition-colors\"\r\n            >\r\n              Retry\r\n            </button>\r\n          </div>\r\n        ) : (\r\n          <div className=\"text-center\">\r\n            <div className=\"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\">\r\n              <Bot className=\"w-8 h-8 text-gray-400\" />\r\n            </div>\r\n            <p className=\"text-sm text-gray-600\">No agent available</p>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CandidateWithAgent;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAAA;AAEA;AACA;AANA;;;;;;;AAoBA,MAAM,qBAAwD,CAAC,EAC7D,YAAY,EAAE,EACd,gBAAgB,UAAU,EAC1B,WAAW,iBAAiB,EAC5B,WAAW,IAAI,EACf,eAAe,KAAK,EACpB,aAAa,UAAU,EACvB,YAAY,EACZ,UAAU,EACX;IACC,MAAM,EACJ,KAAK,EACL,eAAe,EACf,UAAU,EACV,WAAW,EACX,gBAAgB,EAChB,kBAAkB,EAClB,WAAW,EACX,sBAAsB,EACvB,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAEf,MAAM,eAAe,CAAC,kEAAkE,EAAE,SAAS,eAAe,EAAE,cAAc,kGAAkG,CAAC;IACrO,MAAM,YAAY,GAAG,SAAS,YAAY,CAAC;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY,CAAC,SAAS,CAAC,iBAAiB;YAC1C,YAAY,cAAc;QAC5B;IACF,GAAG;QAAC;QAAU;QAAO;QAAiB;QAAa;QAAc;KAAU;IAE3E,wEAAwE;IACxE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS,CAAC,eAAe,eAAe,eAAe,MAAM,KAAK,CAAC,oBAAoB,CAAC,oBAAoB;YAC9G;QACF;IACF,GAAG;QAAC;QAAO;QAAY;QAAkB;QAAoB;KAAuB;IAEpF,qBACE,8OAAC;QAAI,WAAW,CAAC,SAAS,EAAE,WAAW;kBACrC,cAAA,8OAAC;YAAI,WAAU;sBACZ,sBACC,8OAAC;gBAAI,WAAU;0BAEb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,AAAC,eAAe,eAAe,eAAe,uBAC7C,8OAAC,2HAAA,CAAA,UAAY;wBACX,WAAU;wBACV,cAAc;wBACd,YAAY;;;;;6CAGd;;4BACG,MAAM,SAAS,EAAE,0BAChB,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAK,MAAM,SAAS,CAAC,SAAS;gCAC9B,KAAK,MAAM,YAAY;gCACvB,OAAO;gCACP,QAAQ;gCACR,WAAU;gCACV,SAAS,CAAC;oCACR,QAAQ,KAAK,CAAC,gCAAgC,MAAM,SAAS,CAAC,SAAS;oCACvE,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG;oCAChC,EAAE,aAAa,CAAC,kBAAkB,EAAE,UAAU,OAAO;gCACvD;;;;;qDAGF,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;;;;;;0CAKnB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;uBAqBvB,gCACF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;kCACrC,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;uBAE1C,2BACF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;;;;;;kCAEjB,8OAAC;wBAAE,WAAU;kCAA4B;;;;;;kCACzC,8OAAC;wBAAE,WAAU;kCAAyB;;;;;;kCACtC,8OAAC;wBACC,SAAS,IAAM,YAAY,cAAc;wBACzC,WAAU;kCACX;;;;;;;;;;;qCAKH,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;;;;;;kCAEjB,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;;;;;AAMjD;uCAEe", "debugId": null}}, {"offset": {"line": 1357, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/InterviewLayout.tsx"], "sourcesContent": ["import { ReactNode } from \"react\";\r\n\r\nconst InterviewLayout = ({ children }: { children: ReactNode }) => {\r\n  return (\r\n    <div className=\"border rounded-lg p-6 min-h-[600px] mb-4 flex-1\">\r\n      {children}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default InterviewLayout;\r\n"], "names": [], "mappings": ";;;;;AAEA,MAAM,kBAAkB,CAAC,EAAE,QAAQ,EAA2B;IAC5D,qBACE,8OAAC;QAAI,WAAU;kBACZ;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 1379, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/interview/QuestionsPage.tsx"], "sourcesContent": ["\"use client\";\r\nimport { ArrowR<PERSON> } from \"lucide-react\";\r\nimport JobInfoCard from \"@/components/JobInfoCard\";\r\nimport QuestionsList from \"@/components/QuestionsList\";\r\nimport CandidateWithAgent from \"@/components/CandidateWithAgent\";\r\nimport InterviewLayout from \"@/components/InterviewLayout\";\r\nimport { Button } from \"@/components/ui/button\";\r\n\r\ntype QuestionsPageProps = {\r\n  onNext?: () => void;\r\n};\r\n\r\nconst QuestionsPage = ({ onNext }: QuestionsPageProps) => {\r\n  return (\r\n    <div className=\"h-screen\">\r\n      <JobInfoCard />\r\n\r\n      <InterviewLayout>\r\n        <div className=\"flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start\">\r\n          <QuestionsList className=\"h-[550px]\" />\r\n          <CandidateWithAgent\r\n            className=\"h-[550px]\"\r\n            useAgent={true}\r\n            candidateName=\"Jonathan\"\r\n            jobTitle=\"Insurance Agent\"\r\n          />\r\n        </div>\r\n\r\n        <div className=\"flex justify-center mt-10 gap-4\">\r\n          <Button\r\n            variant=\"default\"\r\n            size=\"lg\"\r\n            className=\"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white\"\r\n            onClick={() => onNext && onNext()}\r\n          >\r\n            Start Interview\r\n            <ArrowRight className=\"w-6 h-6 duration-300 group-hover:translate-x-1\" />\r\n          </Button>\r\n        </div>\r\n      </InterviewLayout>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default QuestionsPage;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AANA;;;;;;;;AAYA,MAAM,gBAAgB,CAAC,EAAE,MAAM,EAAsB;IACnD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,0HAAA,CAAA,UAAW;;;;;0BAEZ,8OAAC,8HAAA,CAAA,UAAe;;kCACd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4HAAA,CAAA,UAAa;gCAAC,WAAU;;;;;;0CACzB,8OAAC,iIAAA,CAAA,UAAkB;gCACjB,WAAU;gCACV,UAAU;gCACV,eAAc;gCACd,UAAS;;;;;;;;;;;;kCAIb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,2HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,UAAU;;gCAC1B;8CAEC,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlC;uCAEe", "debugId": null}}, {"offset": {"line": 1481, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/VideoTranscript.tsx"], "sourcesContent": ["const VideoTranscript = () => {\r\n  return (\r\n    <div className=\"rounded-2xl bg-white p-4 w-full max-w-[300px] sm:w-[300px] shadow-sm h-[488px] overflow-y-auto scrollbar-hidden\">\r\n      <p className=\"text-lg font-semibold text-black mb-5\">Video Transcript</p>\r\n      <p>Tell us about yourselves?</p>\r\n      <p className=\"text-sm mt-4 leading-7 \">\r\n        Motivated and results-driven professional with a proven track record of\r\n        success in dynamic work environments. Known for strong problem-solving\r\n        skills, a collaborative mindset, and a dedication to continuous learning\r\n        and improvement. Brings a blend of technical expertise, strategic\r\n        thinking, and effective communication to contribute meaningfully to team\r\n        and organizational goals. Eager to take on new challenges and deliver\r\n        impactful outcomes in a fast-paced role.\r\n      </p>\r\n    </div>\r\n  );\r\n};\r\nexport default VideoTranscript;\r\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,kBAAkB;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAE,WAAU;0BAAwC;;;;;;0BACrD,8OAAC;0BAAE;;;;;;0BACH,8OAAC;gBAAE,WAAU;0BAA0B;;;;;;;;;;;;AAW7C;uCACe", "debugId": null}}, {"offset": {"line": 1527, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/interview/FinishInterview.tsx"], "sourcesContent": ["import { Arrow<PERSON><PERSON> } from \"lucide-react\";\r\nimport JobInfoCard from \"@/components/JobInfoCard\";\r\nimport QuestionsList from \"@/components/QuestionsList\";\r\nimport CandidateWithAgent from \"@/components/CandidateWithAgent\";\r\nimport InterviewLayout from \"@/components/InterviewLayout\";\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport VideoTranscript from \"@/components/VideoTranscript\";\r\n\r\ntype FinishInterviewProps = {\r\n  onNext?: () => void;\r\n};\r\n\r\nconst FinishInterview = ({ onNext }: FinishInterviewProps) => {\r\n  return (\r\n    <div className=\"h-screen\">\r\n      <JobInfoCard />\r\n\r\n      <InterviewLayout>\r\n        <div className=\"flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start\">\r\n          <QuestionsList />\r\n          <CandidateWithAgent\r\n            className=\" h-[490px]\"\r\n            useAgent={true}\r\n            candidateName=\"Jonathan\"\r\n            jobTitle=\"Insurance Agent\"\r\n            message=\"Thank you for completing the interview. Do you have any final questions?\"\r\n          />\r\n          <VideoTranscript />\r\n        </div>\r\n\r\n        <div className=\"flex justify-center mt-10 gap-4\">\r\n          <Button\r\n            variant=\"default\"\r\n            className=\"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white\"\r\n            onClick={() => onNext && onNext()}\r\n          >\r\n            Finish Interview\r\n            <ArrowRight className=\"w-6 h-6 duration-300 group-hover:translate-x-1\" />\r\n          </Button>\r\n        </div>\r\n      </InterviewLayout>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FinishInterview;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAMA,MAAM,kBAAkB,CAAC,EAAE,MAAM,EAAwB;IACvD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,0HAAA,CAAA,UAAW;;;;;0BAEZ,8OAAC,8HAAA,CAAA,UAAe;;kCACd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4HAAA,CAAA,UAAa;;;;;0CACd,8OAAC,iIAAA,CAAA,UAAkB;gCACjB,WAAU;gCACV,UAAU;gCACV,eAAc;gCACd,UAAS;gCACT,SAAQ;;;;;;0CAEV,8OAAC,8HAAA,CAAA,UAAe;;;;;;;;;;;kCAGlB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,2HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,WAAU;4BACV,SAAS,IAAM,UAAU;;gCAC1B;8CAEC,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlC;uCAEe", "debugId": null}}, {"offset": {"line": 1638, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/public/icons/trophy.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 28, height: 28, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAABBUlEQVR42i2JMUvDQBiGv2qpl6slTbSRNLFeqqFncrVJsATUap266F/ooIOLSCY1o11KKDoodWjBRURE0MFdf4H/RLu5Su9KX3jg4X0AS7NoaTEjk2XJCFlufZvjcxefaKBrGXV/S6lusqwdUOwEVHICF9v82yjyBod7ar1Zy3o3UaH7dbvyLbg+K3R3mOQdNJU6BBQ5/UhO3nrk8e+h8S94534X5ROfN6hSTC/a8vHTlX7/8xKOfl/D0XNH75+380esgitAinMLrV3Fd8rI6pxocXKqxcxGVquh+MRAKqRSADV3fpWYSB/G5mB4aQ6Ee25uTbTJhJTNdOmjZ3wKLCNdmpnGMewcPLJUc9zPAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,kHAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAkc,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 1657, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/InterviewCard.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport Image from \"next/image\";\r\nimport TROPHY from \"@/public/icons/trophy.png\";\r\nconst InterviewCard = () => {\r\n  return (\r\n    <div className=\"flex  justify-between bg-white rounded-2xl shadow-md p-4 w-full max-w-xl mb-5\">\r\n      {/* Left Box: Score Section */}\r\n      <div className=\"flex items-center space-x-4\">\r\n        <div className=\"bg-[#F4F1FE] rounded-xl px-4 py-4 text-center w-30\">\r\n          <div className=\"flex justify-center mb-2\">\r\n            <Image src={TROPHY} alt=\"Trophy\" />\r\n          </div>\r\n          <p className=\"text-xl font-bold text-[#1E1E1E]\">55%</p>\r\n          <p className=\"text-xs text-gray-600 mt-1\">Overall Score</p>\r\n        </div>\r\n\r\n        <div>\r\n          <h3 className=\"font-semibold text-sm sm:text-[6px] md:text-base lg:text-lg text-[#1E1E1E] mb-2\">\r\n            AI Interviewer\r\n          </h3>\r\n          <p className=\"text-sm text-gray-800 font-medium\">UI UX Designer</p>\r\n          <p className=\"text-sm text-gray-800 font-medium\">18th June, 2025</p>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"top-0\">\r\n        <span className=\"bg-[#CCFFB1] text-[#1E1E1E] text-xs px-4 py-1 rounded-full\">\r\n          Evaluated\r\n        </span>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default InterviewCard;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AACA,MAAM,gBAAgB;IACpB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCAAC,KAAK,kRAAA,CAAA,UAAM;oCAAE,KAAI;;;;;;;;;;;0CAE1B,8OAAC;gCAAE,WAAU;0CAAmC;;;;;;0CAChD,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;kCAG5C,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAkF;;;;;;0CAGhG,8OAAC;gCAAE,WAAU;0CAAoC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAAoC;;;;;;;;;;;;;;;;;;0BAIrD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAK,WAAU;8BAA6D;;;;;;;;;;;;;;;;;AAMrF;uCAEe", "debugId": null}}, {"offset": {"line": 1780, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/analysis/ScoreBar.jsx"], "sourcesContent": ["const ScoreBar = ({ label, value, color = \"bg-orange-500\" }) => {\r\n  return (\r\n    <div className=\"mb-2\">\r\n      <div className=\"flex justify-between text-sm mb-1\">\r\n        <span className=\"mb-1\">{label}</span>\r\n        <span>{value}/100</span>\r\n      </div>\r\n      <div className=\"w-full bg-gray-200 rounded-full h-2.5\">\r\n        <div\r\n          className={`h-2.5 rounded-full ${color}`}\r\n          style={{ width: `${value}%` }}\r\n        ></div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ScoreBar;\r\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,WAAW,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,eAAe,EAAE;IACzD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;kCAAQ;;;;;;kCACxB,8OAAC;;4BAAM;4BAAM;;;;;;;;;;;;;0BAEf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,WAAW,CAAC,mBAAmB,EAAE,OAAO;oBACxC,OAAO;wBAAE,OAAO,GAAG,MAAM,CAAC,CAAC;oBAAC;;;;;;;;;;;;;;;;;AAKtC;uCAEe", "debugId": null}}, {"offset": {"line": 1847, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/analysis/CircularRating.jsx"], "sourcesContent": ["import { CircularProgressbar, buildStyles } from \"react-circular-progressbar\";\r\nimport \"react-circular-progressbar/dist/styles.css\";\r\n\r\nconst CircularRating = ({ label, percent, color, trailColor }) => {\r\n  return (\r\n    <div className=\"flex flex-col items-center space-y-1 mb-2\">\r\n      <p className=\"text-sm font-semibold mb-3\">{label}</p>\r\n      <div className=\"w-32 h-28\">\r\n        <CircularProgressbar\r\n          value={percent}\r\n          text={`${percent}%`}\r\n          strokeWidth={10}\r\n          styles={buildStyles({\r\n            textSize: \"12px\",\r\n            pathColor: color,\r\n            textColor: \"#5a5a5a\",\r\n            trailColor: trailColor,\r\n          })}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CircularRating;\r\n"], "names": [], "mappings": ";;;;AAAA;;;;AAGA,MAAM,iBAAiB,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE;IAC3D,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAE,WAAU;0BAA8B;;;;;;0BAC3C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,wKAAA,CAAA,sBAAmB;oBAClB,OAAO;oBACP,MAAM,GAAG,QAAQ,CAAC,CAAC;oBACnB,aAAa;oBACb,QAAQ,CAAA,GAAA,wKAAA,CAAA,cAAW,AAAD,EAAE;wBAClB,UAAU;wBACV,WAAW;wBACX,WAAW;wBACX,YAAY;oBACd;;;;;;;;;;;;;;;;;AAKV;uCAEe", "debugId": null}}, {"offset": {"line": 1903, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/analysis/ScoreCard.jsx"], "sourcesContent": ["import ScoreBar from \"./ScoreBar\";\r\nimport CircularRating from \"./CircularRating\";\r\n\r\nconst ScoreCard = () => {\r\n  return (\r\n    <div className=\"grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 border p-6 rounded-xl w-full max-w-6xl mx-auto\">\r\n      {/* Resume Score */}\r\n      <div className=\"bg-white rounded-lg p-4 shadow-sm\">\r\n        <div className=\"flex justify-between font-semibold mb-4\">\r\n          <span>Resume Score</span>\r\n          <span>65%</span>\r\n        </div>\r\n        <div className=\"flex flex-col gap-4\">\r\n          <ScoreBar label=\"Company Fit\" value={66} />\r\n          <ScoreBar\r\n            label=\"Relevant Experience\"\r\n            value={66}\r\n            color=\"bg-purple-600\"\r\n          />\r\n          <ScoreBar label=\"Job Knowledge\" value={66} />\r\n          <ScoreBar label=\"Education\" value={66} />\r\n          <ScoreBar label=\"Hard Skills\" value={66} />\r\n        </div>\r\n\r\n        <div className=\"mt-4 font-medium flex justify-between bg-gray-100 text-sm text-center border rounded-xl p-8\">\r\n          Over All Score &nbsp; <span className=\"text-black\">66/100</span>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Video Score */}\r\n      <div className=\"bg-white rounded-lg p-4 shadow-sm\">\r\n        <div className=\"font-semibold mb-4\">Video Score</div>\r\n        <div className=\"flex flex-col gap-4\">\r\n          <ScoreBar label=\"Professionalism\" value={64} />\r\n          <ScoreBar label=\"Energy Level\" value={56} color=\"bg-purple-600\" />\r\n          <ScoreBar label=\"Communication\" value={58} />\r\n          <ScoreBar label=\"Sociability\" value={70} />\r\n        </div>\r\n      </div>\r\n\r\n      {/* AI Ratings */}\r\n      <div className=\"bg-white rounded-lg p-4 flex flex-col space-y-2   gap-5 shadow-sm\">\r\n        <p className=\"font-semibold\">AI Rating</p>\r\n        <CircularRating\r\n          label=\"AI Resume Rating\"\r\n          percent={75}\r\n          color=\"#A855F7\"\r\n          trailColor=\"#EAE2FF\"\r\n        />\r\n        <CircularRating\r\n          label=\"AI Video Rating\"\r\n          percent={75}\r\n          color=\"#FF5B00\"\r\n          trailColor=\"#FFEAE1\"\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ScoreCard;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEA,MAAM,YAAY;IAChB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAK;;;;;;0CACN,8OAAC;0CAAK;;;;;;;;;;;;kCAER,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,mIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAc,OAAO;;;;;;0CACrC,8OAAC,mIAAA,CAAA,UAAQ;gCACP,OAAM;gCACN,OAAO;gCACP,OAAM;;;;;;0CAER,8OAAC,mIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAgB,OAAO;;;;;;0CACvC,8OAAC,mIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAY,OAAO;;;;;;0CACnC,8OAAC,mIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAc,OAAO;;;;;;;;;;;;kCAGvC,8OAAC;wBAAI,WAAU;;4BAA8F;0CACrF,8OAAC;gCAAK,WAAU;0CAAa;;;;;;;;;;;;;;;;;;0BAKvD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAqB;;;;;;kCACpC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,mIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAkB,OAAO;;;;;;0CACzC,8OAAC,mIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAe,OAAO;gCAAI,OAAM;;;;;;0CAChD,8OAAC,mIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAgB,OAAO;;;;;;0CACvC,8OAAC,mIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAc,OAAO;;;;;;;;;;;;;;;;;;0BAKzC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;kCAC7B,8OAAC,yIAAA,CAAA,UAAc;wBACb,OAAM;wBACN,SAAS;wBACT,OAAM;wBACN,YAAW;;;;;;kCAEb,8OAAC,yIAAA,CAAA,UAAc;wBACb,OAAM;wBACN,SAAS;wBACT,OAAM;wBACN,YAAW;;;;;;;;;;;;;;;;;;AAKrB;uCAEe", "debugId": null}}, {"offset": {"line": 2126, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/interview/Analysis.tsx"], "sourcesContent": ["// import JobInfoCard from \"@/components/JobInfoCard\";\r\nimport QuestionsList from \"@/components/QuestionsList\";\r\nimport CandidateWithAgent from \"@/components/CandidateWithAgent\";\r\nimport InterviewLayout from \"@/components/InterviewLayout\";\r\nimport VideoTranscript from \"@/components/VideoTranscript\";\r\nimport InterviewCard from \"@/components/InterviewCard\";\r\nimport ScoreCard from \"../analysis/ScoreCard\";\r\n\r\nconst Analysis = () => {\r\n  return (\r\n    <div className=\"h-screen\">\r\n      <InterviewCard />\r\n      <InterviewLayout>\r\n        <div className=\"flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start\">\r\n          <QuestionsList />\r\n          <CandidateWithAgent\r\n            className=\"h-[490px]\"\r\n            useAgent={false} \r\n            candidateName=\"Jonathan\"\r\n            jobTitle=\"Insurance Agent\"\r\n          />\r\n          <VideoTranscript />\r\n        </div>\r\n      </InterviewLayout>\r\n      <ScoreCard />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Analysis;\r\n"], "names": [], "mappings": "AAAA,sDAAsD;;;;;AACtD;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEA,MAAM,WAAW;IACf,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,UAAa;;;;;0BACd,8OAAC,8HAAA,CAAA,UAAe;0BACd,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4HAAA,CAAA,UAAa;;;;;sCACd,8OAAC,iIAAA,CAAA,UAAkB;4BACjB,WAAU;4BACV,UAAU;4BACV,eAAc;4BACd,UAAS;;;;;;sCAEX,8OAAC,8HAAA,CAAA,UAAe;;;;;;;;;;;;;;;;0BAGpB,8OAAC,oIAAA,CAAA,UAAS;;;;;;;;;;;AAGhB;uCAEe", "debugId": null}}, {"offset": {"line": 2207, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/interview/InterviewRecording.tsx"], "sourcesContent": ["import { ArrowR<PERSON> } from \"lucide-react\";\nimport JobInfoCard from \"@/components/JobInfoCard\";\nimport QuestionsList from \"@/components/QuestionsList\";\nimport InterviewLayout from \"@/components/InterviewLayout\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport CandidateWithAgent from \"../CandidateWithAgent\";\n\ntype InterviewRecordingProps = {\n  onNext?: () => void;\n};\n\nconst InterviewRecording = ({ onNext }: InterviewRecordingProps) => {\n  return (\n    <div className=\"h-screen\">\n      <JobInfoCard />\n\n      <InterviewLayout>\n        <div className=\"flex flex-col md:flex-row gap-10 justify-center items-center md:items-start\">\n          <QuestionsList className=\"h-[550px]\" />\n          <CandidateWithAgent\n            className=\"h-[550px]\"\n            useAgent={true}\n            candidateName=\"Jonathan\"\n            jobTitle=\"Insurance Agent\"\n          />\n        </div>\n\n        <div className=\"flex justify-center mt-10 gap-4\">\n          <Button\n            // disabled\n            variant=\"default\"\n            className=\"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white\"\n            onClick={() => onNext && onNext()}\n          >\n            Start Interview\n            <ArrowRight className=\"w-6 h-6 duration-300 group-hover:translate-x-1\" />\n          </Button>\n        </div>\n        <div className=\"flex justify-center mt-5 text-2xl font-semibold text-primary\">\n          02:00\n        </div>\n      </InterviewLayout>\n    </div>\n  );\n};\n\nexport default InterviewRecording;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAMA,MAAM,qBAAqB,CAAC,EAAE,MAAM,EAA2B;IAC7D,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,0HAAA,CAAA,UAAW;;;;;0BAEZ,8OAAC,8HAAA,CAAA,UAAe;;kCACd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4HAAA,CAAA,UAAa;gCAAC,WAAU;;;;;;0CACzB,8OAAC,iIAAA,CAAA,UAAkB;gCACjB,WAAU;gCACV,UAAU;gCACV,eAAc;gCACd,UAAS;;;;;;;;;;;;kCAIb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,2HAAA,CAAA,SAAM;4BACL,WAAW;4BACX,SAAQ;4BACR,WAAU;4BACV,SAAS,IAAM,UAAU;;gCAC1B;8CAEC,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAG1B,8OAAC;wBAAI,WAAU;kCAA+D;;;;;;;;;;;;;;;;;;AAMtF;uCAEe", "debugId": null}}, {"offset": {"line": 2316, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/app/%28root%29/interview/page.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState } from \"react\";\r\nimport InterviewInstructions from \"@/components/interview/InterviewInstructions\";\r\nimport QuestionsPage from \"@/components/interview/QuestionsPage\";\r\nimport FinishInterview from \"@/components/interview/FinishInterview\";\r\nimport Analysis from \"@/components/interview/Analysis\";\r\nimport InterviewRecording from \"../../../components/interview/InterviewRecording\";\r\nimport { InterviewProvider } from \"@/context/InterviewContext\";\r\n\r\ntype InterviewStep =\r\n  | \"instructions\"\r\n  | \"questions\"\r\n  | \"recording\"\r\n  | \"finishInterview\"\r\n  | \"analysis\";\r\n\r\nconst Interview = () => {\r\n  const [currentStep, setCurrentStep] = useState<InterviewStep>(\"instructions\");\r\n\r\n  const renderCurrentComponent = () => {\r\n    switch (currentStep) {\r\n      case \"instructions\":\r\n        return (\r\n          <InterviewInstructions onNext={() => setCurrentStep(\"questions\")} />\r\n        );\r\n      case \"questions\":\r\n        return <QuestionsPage onNext={() => setCurrentStep(\"recording\")} />;\r\n      case \"recording\":\r\n        return (\r\n          <InterviewRecording\r\n            onNext={() => setCurrentStep(\"finishInterview\")}\r\n          />\r\n        );\r\n      case \"finishInterview\":\r\n        return <FinishInterview onNext={() => setCurrentStep(\"analysis\")} />;\r\n      case \"analysis\":\r\n        return <Analysis />;\r\n      default:\r\n        return (\r\n          <InterviewInstructions onNext={() => setCurrentStep(\"questions\")} />\r\n        );\r\n    }\r\n  };\r\n\r\n  return (\r\n    <InterviewProvider>\r\n      <div>{renderCurrentComponent()}</div>\r\n    </InterviewProvider>\r\n  );\r\n};\r\n\r\nexport default Interview;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;;AAgBA,MAAM,YAAY;IAChB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE9D,MAAM,yBAAyB;QAC7B,OAAQ;YACN,KAAK;gBACH,qBACE,8OAAC,iJAAA,CAAA,UAAqB;oBAAC,QAAQ,IAAM,eAAe;;;;;;YAExD,KAAK;gBACH,qBAAO,8OAAC,yIAAA,CAAA,UAAa;oBAAC,QAAQ,IAAM,eAAe;;;;;;YACrD,KAAK;gBACH,qBACE,8OAAC,8IAAA,CAAA,UAAkB;oBACjB,QAAQ,IAAM,eAAe;;;;;;YAGnC,KAAK;gBACH,qBAAO,8OAAC,2IAAA,CAAA,UAAe;oBAAC,QAAQ,IAAM,eAAe;;;;;;YACvD,KAAK;gBACH,qBAAO,8OAAC,oIAAA,CAAA,UAAQ;;;;;YAClB;gBACE,qBACE,8OAAC,iJAAA,CAAA,UAAqB;oBAAC,QAAQ,IAAM,eAAe;;;;;;QAE1D;IACF;IAEA,qBACE,8OAAC,4HAAA,CAAA,oBAAiB;kBAChB,cAAA,8OAAC;sBAAK;;;;;;;;;;;AAGZ;uCAEe", "debugId": null}}]}