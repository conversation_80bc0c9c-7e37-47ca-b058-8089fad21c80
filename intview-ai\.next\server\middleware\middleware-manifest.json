{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_5399b416._.js", "server/edge/chunks/node_modules_@auth_core_5ebafa38._.js", "server/edge/chunks/node_modules_jose_dist_webapi_49ff121e._.js", "server/edge/chunks/node_modules_e184ff1b._.js", "server/edge/chunks/[root-of-the-server]__df53d061._.js", "server/edge/chunks/edge-wrapper_3d09a47d.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "EgkodQbEY9m1pIgxkdt6YkPs3VngzrM6PXdSaRub/rI=", "__NEXT_PREVIEW_MODE_ID": "fed1d91061ed2e671ce4ed1bdce5e8d9", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7ebe287514c314725f4231d2fb1244858779682f3518f4cf243a88f30cb821e4", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d55c80799c92efd89270ac040802e72c094b7696d1f3f5dce7d28a102889f4c8"}}}, "instrumentation": null, "functions": {}}