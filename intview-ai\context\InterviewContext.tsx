"use client";
import React, { createContext, useContext, useState, useCallback, ReactNode } from "react";

const DID_API_URL = "https://api.d-id.com";

interface Agent {
  id: string;
  preview_name: string;
  status: string;
  presenter: {
    type: string;
    voice: {
      type: string;
      voice_id: string;
    };
    thumbnail: string;
    source_url: string;
  };
  llm: {
    type: string;
    provider: string;
    model: string;
    instructions: string;
  };
}

interface ChatSession {
  id: string;
  agent_id: string;
  created: string;
  modified: string;
  messages: Array<{
    role: string;
    content: string;
    created_at: string;
  }>;
  owner_id: string;
}

interface StreamConnection {
  streamId: string;
  sessionId: string;
  iceServers: Array<{
    urls: string[];
    username?: string;
    credential?: string;
  }>;
}

interface InterviewContextType {
  // D-ID Agent state
  agent: Agent | null;
  isCreatingAgent: boolean;
  agentError: string | null;
  createAgent: (instructions: string, agentName: string) => Promise<void>;

  // Chat Session state
  chatSession: ChatSession | null;
  isCreatingChat: boolean;
  chatError: string | null;
  createChatSession: () => Promise<void>;

  // Stream Connection state
  streamConnection: StreamConnection | null;
  isConnectingStream: boolean;
  streamError: string | null;
  createStreamConnection: () => Promise<void>;

  // Message sending
  isSendingMessage: boolean;
  messageError: string | null;
  sendMessage: (message: string) => Promise<void>;

  // Interview state
  currentQuestion: number;
  setCurrentQuestion: (question: number) => void;
  isInterviewStarted: boolean;
  setIsInterviewStarted: (started: boolean) => void;

  // Questions data
  questions: string[];
}

const InterviewContext = createContext<InterviewContextType | undefined>(undefined);

interface InterviewProviderProps {
  children: ReactNode;
}

export const InterviewProvider: React.FC<InterviewProviderProps> = ({ children }) => {
  // D-ID Agent state
  const [agent, setAgent] = useState<Agent | null>(null);
  const [isCreatingAgent, setIsCreatingAgent] = useState<boolean>(false);
  const [agentError, setAgentError] = useState<string | null>(null);

  // Chat Session state
  const [chatSession, setChatSession] = useState<ChatSession | null>(null);
  const [isCreatingChat, setIsCreatingChat] = useState<boolean>(false);
  const [chatError, setChatError] = useState<string | null>(null);

  // Stream Connection state
  const [streamConnection, setStreamConnection] = useState<StreamConnection | null>(null);
  const [isConnectingStream, setIsConnectingStream] = useState<boolean>(false);
  const [streamError, setStreamError] = useState<string | null>(null);

  // Message sending state
  const [isSendingMessage, setIsSendingMessage] = useState<boolean>(false);
  const [messageError, setMessageError] = useState<string | null>(null);

  // Interview state
  const [currentQuestion, setCurrentQuestion] = useState<number>(1);
  const [isInterviewStarted, setIsInterviewStarted] = useState<boolean>(false);
  
  // Questions data
  const questions = [
    "Tell us about yourself?",
    "What are your strengths?",
    "Why do you want this job?",
    "Where do you see yourself in 5 years?",
  ];

  const getAuthHeaders = () => {
    const apiKey = process.env.NEXT_PUBLIC_DID_API_KEY || process.env.DID_API_KEY || "";
    console.log("Using D-ID API Key:", apiKey ? `${apiKey.substring(0, 10)}...` : "NOT_FOUND");

    return {
      "Authorization": `Basic ${apiKey}`,
      "Content-Type": "application/json",
    };
  };

  const createAgent = useCallback(async (instructions: string, agentName: string) => {
    // If agent already exists with same instructions, don't recreate
    if (agent && agent.llm.instructions === instructions && agent.preview_name === agentName) {
      return;
    }

    setIsCreatingAgent(true);
    setAgentError(null);

    const payload = {
      presenter: {
        type: "talk",
        voice: {
          type: "microsoft",
          voice_id: "en-US-JennyMultilingualV2Neural"
        },
        thumbnail: "https://create-images-results.d-id.com/DefaultPresenters/Zivva_f/thumbnail.jpeg",
        source_url: "https://create-images-results.d-id.com/DefaultPresenters/Zivva_f/thumbnail.jpeg"
      },
      llm: {
        type: "openai",
        provider: "openai",
        model: "gpt-4o-mini",
        instructions: instructions
      },
      preview_name: agentName
    };

    try {
      console.log("Creating D-ID Agent with payload:", payload);

      const response = await fetch(`${DID_API_URL}/agents`, {
        method: "POST",
        headers: getAuthHeaders(),
        body: JSON.stringify(payload),
      });

      console.log("D-ID Agent API Response Status:", response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error("D-ID Agent API Error Response:", errorText);
        throw new Error(`Failed to create agent: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const agentData: Agent = await response.json();
      console.log("D-ID Agent Created Successfully:", agentData);
      setAgent(agentData);
    } catch (err: unknown) {
      console.error("D-ID Agent Creation Error:", err);
      const errorMessage = err instanceof Error ? err.message : "Failed to create agent";
      setAgentError(`Agent Creation Failed: ${errorMessage}`);
    } finally {
      setIsCreatingAgent(false);
    }
  }, [agent]);

  const createChatSession = useCallback(async () => {
    if (!agent) {
      setChatError("No agent available to create chat session");
      return;
    }

    setIsCreatingChat(true);
    setChatError(null);

    try {
      console.log("Creating chat session for agent:", agent.id);

      const response = await fetch(`${DID_API_URL}/agents/${agent.id}/chat`, {
        method: "POST",
        headers: getAuthHeaders(),
        body: JSON.stringify({}), // Empty body as per documentation
      });

      console.log("Chat Session API Response Status:", response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Chat Session API Error Response:", errorText);
        throw new Error(`Failed to create chat session: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const chatData: ChatSession = await response.json();
      console.log("Chat Session Created Successfully:", chatData);
      setChatSession(chatData);
    } catch (err: unknown) {
      console.error("Chat Session Creation Error:", err);
      const errorMessage = err instanceof Error ? err.message : "Failed to create chat session";
      setChatError(`Chat Session Creation Failed: ${errorMessage}`);
    } finally {
      setIsCreatingChat(false);
    }
  }, [agent]);

  const createStreamConnection = useCallback(async () => {
    if (!agent) {
      setStreamError("No agent available to create stream connection");
      return;
    }

    setIsConnectingStream(true);
    setStreamError(null);

    try {
      console.log("Creating stream connection for agent:", agent.id);

      // Step 1: Create a new stream
      const streamResponse = await fetch(`${DID_API_URL}/agents/${agent.id}/streams`, {
        method: "POST",
        headers: getAuthHeaders(),
        body: JSON.stringify({}), // Empty body as per documentation
      });

      if (!streamResponse.ok) {
        const errorText = await streamResponse.text();
        throw new Error(`Failed to create stream: ${streamResponse.status} ${streamResponse.statusText} - ${errorText}`);
      }

      const streamData = await streamResponse.json();
      console.log("Stream Created Successfully:", streamData);

      const connectionData: StreamConnection = {
        streamId: streamData.id,
        sessionId: streamData.session_id,
        iceServers: streamData.ice_servers || []
      };

      setStreamConnection(connectionData);
    } catch (err: unknown) {
      console.error("Stream Connection Creation Error:", err);
      const errorMessage = err instanceof Error ? err.message : "Failed to create stream connection";
      setStreamError(`Stream Connection Failed: ${errorMessage}`);
    } finally {
      setIsConnectingStream(false);
    }
  }, [agent]);

  const sendMessage = useCallback(async (message: string) => {
    if (!agent || !chatSession || !streamConnection) {
      setMessageError("Agent, chat session, or stream connection not available");
      return;
    }

    setIsSendingMessage(true);
    setMessageError(null);

    try {
      console.log("Sending message to agent:", message);

      const messagePayload = {
        streamId: streamConnection.streamId,
        sessionId: streamConnection.sessionId,
        messages: [
          {
            role: "user",
            content: message,
            created_at: new Date().toLocaleString()
          }
        ]
      };

      const response = await fetch(`${DID_API_URL}/agents/${agent.id}/chat/${chatSession.id}`, {
        method: "POST",
        headers: getAuthHeaders(),
        body: JSON.stringify(messagePayload),
      });

      console.log("Send Message API Response Status:", response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Send Message API Error Response:", errorText);
        throw new Error(`Failed to send message: ${response.status} ${response.statusText} - ${errorText}`);
      }

      console.log("Message sent successfully");
      // The video response will be streamed via WebRTC connection
    } catch (err: unknown) {
      console.error("Send Message Error:", err);
      const errorMessage = err instanceof Error ? err.message : "Failed to send message";
      setMessageError(`Send Message Failed: ${errorMessage}`);
    } finally {
      setIsSendingMessage(false);
    }
  }, [agent, chatSession, streamConnection]);

  const value: InterviewContextType = {
    // D-ID Agent state
    agent,
    isCreatingAgent,
    agentError,
    createAgent,

    // Chat Session state
    chatSession,
    isCreatingChat,
    chatError,
    createChatSession,

    // Stream Connection state
    streamConnection,
    isConnectingStream,
    streamError,
    createStreamConnection,

    // Message sending
    isSendingMessage,
    messageError,
    sendMessage,

    // Interview state
    currentQuestion,
    setCurrentQuestion,
    isInterviewStarted,
    setIsInterviewStarted,

    // Questions data
    questions,
  };

  return (
    <InterviewContext.Provider value={value}>
      {children}
    </InterviewContext.Provider>
  );
};

export const useInterview = (): InterviewContextType => {
  const context = useContext(InterviewContext);
  if (context === undefined) {
    throw new Error('useInterview must be used within an InterviewProvider');
  }
  return context;
};
