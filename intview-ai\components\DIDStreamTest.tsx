"use client";
import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";

const DIDStreamTest: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const testDIDAPI = async () => {
    setIsLoading(true);
    setError(null);
    setResult(null);

    try {
      const apiKey = process.env.NEXT_PUBLIC_DID_API_KEY || process.env.DID_API_KEY || "";
      console.log("Testing D-ID API with key:", apiKey ? `${apiKey.substring(0, 10)}...` : "NOT_FOUND");

      if (!apiKey) {
        throw new Error("D-ID API key not found");
      }

      // Test 1: Create agent
      console.log("Step 1: Creating agent...");
      const agentResponse = await fetch("https://api.d-id.com/agents", {
        method: "POST",
        headers: {
          "Authorization": `Basic ${apiKey}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          presenter: {
            type: "talk",
            voice: {
              type: "microsoft",
              voice_id: "en-US-JennyMultilingualV2Neural"
            },
            thumbnail: "https://create-images-results.d-id.com/DefaultPresenters/Zivva_f/thumbnail.jpeg",
            source_url: "https://create-images-results.d-id.com/DefaultPresenters/Zivva_f/thumbnail.jpeg"
          },
          llm: {
            type: "openai",
            provider: "openai",
            model: "gpt-4o-mini",
            instructions: "You are a test assistant."
          },
          preview_name: "Test Agent"
        }),
      });

      if (!agentResponse.ok) {
        const errorText = await agentResponse.text();
        throw new Error(`Agent creation failed: ${agentResponse.status} - ${errorText}`);
      }

      const agentData = await agentResponse.json();
      console.log("Agent created:", agentData);

      // Test 2: Create stream
      console.log("Step 2: Creating stream...");
      const streamResponse = await fetch("https://api.d-id.com/talks/streams", {
        method: "POST",
        headers: {
          "Authorization": `Basic ${apiKey}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          source_url: agentData.presenter.source_url
        }),
      });

      if (!streamResponse.ok) {
        const errorText = await streamResponse.text();
        throw new Error(`Stream creation failed: ${streamResponse.status} - ${errorText}`);
      }

      const streamData = await streamResponse.json();
      console.log("Stream created:", streamData);

      setResult({
        agent: agentData,
        stream: streamData,
        success: true
      });

    } catch (err: any) {
      console.error("D-ID API Test Error:", err);
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg max-w-2xl mx-auto">
      <h2 className="text-xl font-bold mb-4">D-ID API Test</h2>
      
      <Button 
        onClick={testDIDAPI} 
        disabled={isLoading}
        className="mb-4"
      >
        {isLoading ? "Testing..." : "Test D-ID API"}
      </Button>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <strong>Error:</strong> {error}
        </div>
      )}

      {result && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
          <strong>Success!</strong>
          <pre className="mt-2 text-xs overflow-auto">
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
};

export default DIDStreamTest;
